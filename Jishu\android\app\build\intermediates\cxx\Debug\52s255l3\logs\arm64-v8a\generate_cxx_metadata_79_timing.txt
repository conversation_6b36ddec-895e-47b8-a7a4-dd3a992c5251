# C/C++ build system timings
generate_cxx_metadata
  [gap of 35ms]
  create-invalidation-state 47ms
  [gap of 16ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 120ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 29ms]
  create-invalidation-state 31ms
  [gap of 13ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 86ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 72ms
  [gap of 90ms]
generate_cxx_metadata completed in 169ms

