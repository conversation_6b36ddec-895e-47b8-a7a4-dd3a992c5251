# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 26ms
generate_cxx_metadata completed in 49ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 15ms
generate_cxx_metadata completed in 26ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 31ms
  [gap of 22ms]
generate_cxx_metadata completed in 53ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 27ms
  [gap of 11ms]
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 22ms
  [gap of 13ms]
generate_cxx_metadata completed in 41ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 39ms
  [gap of 13ms]
generate_cxx_metadata completed in 74ms

