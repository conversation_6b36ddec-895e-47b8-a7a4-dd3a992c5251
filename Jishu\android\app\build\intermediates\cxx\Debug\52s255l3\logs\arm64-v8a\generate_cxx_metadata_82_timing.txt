# C/C++ build system timings
generate_cxx_metadata
  [gap of 41ms]
  create-invalidation-state 42ms
  [gap of 17ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 126ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 29ms
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 74ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 43ms
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 81ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 38ms]
  create-invalidation-state 51ms
  [gap of 11ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 122ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 33ms
  [gap of 12ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 100ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 353ms]
  create-invalidation-state 77ms
  [gap of 25ms]
  write-metadata-json-to-file 29ms
generate_cxx_metadata completed in 485ms

